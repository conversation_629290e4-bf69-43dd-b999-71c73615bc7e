"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { 
  Search, 
  Filter, 
  BookOpen, 
  Clock, 
  Users, 
  Star,
  Calendar,
  Target,
  Play,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Award,
  Zap,
  Brain,
  Globe,
  Lock,
  Eye,
  Heart,
  Share2
} from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { motion, AnimatePresence } from "framer-motion"
import { toast } from "@/lib/toast-utils"
import { QuizFavoriteButton } from '@/components/student/quiz-favorite-button'
import { QuizShareButton } from '@/components/student/quiz-share-button'
import Link from "next/link"
import { CategoryFilter } from "@/components/admin/category-filter"

interface Quiz {
  id: string
  title: string
  description: string
  type: 'QUIZ' | 'TEST_SERIES' | 'DAILY_PRACTICE'
  difficulty: 'EASY' | 'MEDIUM' | 'HARD'
  tags: string[]
  thumbnail?: string
  timeLimit: number
  questionCount: number
  totalAttempts: number
  enrollmentCount: number
  isEnrolled: boolean
  lastAttempt?: {
    id: string
    score: number
    percentage: number
    completedAt: string
  } | null
  instructor: {
    name: string
    email: string
  }
  createdAt: string
  startTime?: string | null
  endTime?: string | null
}

export default function BrowseQuizzes() {
  const [quizzes, setQuizzes] = useState<Quiz[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [categoryFilters, setCategoryFilters] = useState<{
    subjectId?: string
    chapterId?: string
    topicId?: string
  }>({})
  const [selectedDifficulty, setSelectedDifficulty] = useState("all")
  const [selectedType, setSelectedType] = useState("all")
  const [sortBy, setSortBy] = useState("popular")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [showFilters, setShowFilters] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalQuizzes, setTotalQuizzes] = useState(0)
  const [enrollmentFilter, setEnrollmentFilter] = useState("all")

  useEffect(() => {
    fetchQuizzes()
  }, [searchQuery, categoryFilters, selectedDifficulty, selectedType, sortBy, currentPage, enrollmentFilter])

  const fetchQuizzes = async () => {
    setLoading(true)
    try {
      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '12',
        ...(searchQuery && { search: searchQuery }),
        ...(selectedType !== 'all' && { type: selectedType }),
        ...(selectedDifficulty !== 'all' && { difficulty: selectedDifficulty }),
        ...(categoryFilters.subjectId && { subjectId: categoryFilters.subjectId }),
        ...(categoryFilters.chapterId && { chapterId: categoryFilters.chapterId }),
        ...(categoryFilters.topicId && { topicId: categoryFilters.topicId }),
        ...(enrollmentFilter !== 'all' && { enrolled: enrollmentFilter === 'enrolled' ? 'true' : 'false' })
      })

      const response = await fetch(`/api/student/quizzes?${params}`)

      if (!response.ok) {
        throw new Error('Failed to fetch quizzes')
      }

      const data = await response.json()

      if (data.success) {
        setQuizzes(data.data)
        setTotalPages(data.pagination.pages)
        setTotalQuizzes(data.pagination.total)
      } else {
        throw new Error(data.message || 'Failed to fetch quizzes')
      }



    } catch (error) {
      console.error('Error fetching quizzes:', error)
      toast.error('Failed to load quizzes')
    } finally {
      setLoading(false)
    }
  }

  const handleEnroll = async (quizId: string) => {
    try {
      console.log('Attempting to enroll in quiz:', quizId)

      const response = await fetch(`/api/student/quizzes/${quizId}/enroll`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })

      console.log('Enrollment response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('Enrollment error:', errorData)
        throw new Error(errorData.message || `HTTP ${response.status}: Failed to enroll`)
      }

      const data = await response.json()
      console.log('Enrollment response data:', data)

      if (data.success) {
        // Update local state
        setQuizzes(prev =>
          prev.map(quiz =>
            quiz.id === quizId
              ? { ...quiz, isEnrolled: true, enrollmentCount: quiz.enrollmentCount + 1 }
              : quiz
          )
        )
        toast.success('Successfully enrolled in quiz!')
      } else {
        throw new Error(data.message || 'Failed to enroll')
      }
    } catch (error) {
      console.error('Enrollment error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to enroll in quiz')
    }
  }



  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY':
        return 'text-green-600 bg-green-50 border-green-200 dark:text-green-400 dark:bg-green-900/20 dark:border-green-800'
      case 'MEDIUM':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200 dark:text-yellow-400 dark:bg-yellow-900/20 dark:border-yellow-800'
      case 'HARD':
        return 'text-red-600 bg-red-50 border-red-200 dark:text-red-400 dark:bg-red-900/20 dark:border-red-800'
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200 dark:text-gray-400 dark:bg-gray-900/20 dark:border-gray-800'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'QUIZ':
        return <BookOpen className="h-4 w-4" />
      case 'TEST_SERIES':
        return <Target className="h-4 w-4" />
      case 'DAILY_PRACTICE':
        return <Zap className="h-4 w-4" />
      default:
        return <BookOpen className="h-4 w-4" />
    }
  }

  // Since filtering is now done on the server side via API params,
  // we don't need client-side filtering anymore
  const filteredQuizzes = quizzes

  const sortedQuizzes = [...filteredQuizzes].sort((a, b) => {
    switch (sortBy) {
      case 'popular':
        return b.totalAttempts - a.totalAttempts
      case 'newest':
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      case 'difficulty':
        const difficultyOrder = { 'EASY': 1, 'MEDIUM': 2, 'HARD': 3 }
        return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty]
      default:
        return 0
    }
  })

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Browse Quizzes</h1>
          <p className="text-muted-foreground mt-1">
            Discover and enroll in quizzes to enhance your learning
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter className="h-4 w-4 mr-2" />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
          </Button>
        </div>
      </div>

      {/* Search and Quick Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Search quizzes by title, description, or tags..."
                className="pl-10"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>

            {/* Advanced Filters */}
            <AnimatePresence>
              {showFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-4 pt-4 border-t"
                >
                  {/* Category Filter */}
                  <div>
                    <label className="text-sm font-medium mb-2 block">Categories</label>
                    <CategoryFilter
                      selectedSubjectId={categoryFilters.subjectId}
                      selectedChapterId={categoryFilters.chapterId}
                      selectedTopicId={categoryFilters.topicId}
                      onFilterChange={setCategoryFilters}
                      userRole="STUDENT"
                    />
                  </div>

                  {/* Other Filters */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Difficulty</label>
                      <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Levels</SelectItem>
                          <SelectItem value="EASY">Easy</SelectItem>
                          <SelectItem value="MEDIUM">Medium</SelectItem>
                          <SelectItem value="HARD">Hard</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Type</label>
                      <Select value={selectedType} onValueChange={setSelectedType}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Types</SelectItem>
                          <SelectItem value="QUIZ">Quiz</SelectItem>
                          <SelectItem value="TEST_SERIES">Test Series</SelectItem>
                          <SelectItem value="DAILY_PRACTICE">Daily Practice</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <label className="text-sm font-medium mb-2 block">Sort By</label>
                      <Select value={sortBy} onValueChange={setSortBy}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="popular">Most Popular</SelectItem>
                          <SelectItem value="rating">Highest Rated</SelectItem>
                          <SelectItem value="newest">Newest</SelectItem>
                          <SelectItem value="difficulty">Difficulty</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {sortedQuizzes.length} of {quizzes.length} quizzes
        </p>
        <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "grid" | "list")}>
          <TabsList>
            <TabsTrigger value="grid">Grid</TabsTrigger>
            <TabsTrigger value="list">List</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Quiz Grid/List */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="pt-6">
                <div className="space-y-3">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                  <div className="h-3 bg-muted rounded w-full"></div>
                  <div className="h-8 bg-muted rounded w-1/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : sortedQuizzes.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <BookOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">No quizzes found</h3>
              <p className="text-muted-foreground mb-6">
                Try adjusting your search criteria or filters to find more quizzes.
              </p>
              <Button onClick={() => {
                setSearchQuery("")
                setCategoryFilters({})
                setSelectedDifficulty("all")
                setSelectedType("all")
              }}>
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className={viewMode === 'grid' 
          ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          : "space-y-4"
        }>
          {sortedQuizzes.map((quiz, index) => (
            <motion.div
              key={quiz.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 * index }}
            >
              <Card className="group relative overflow-hidden border-0 bg-gradient-to-br from-white/80 to-white/40 dark:from-gray-900/80 dark:to-gray-800/40 backdrop-blur-sm hover:shadow-2xl hover:shadow-primary/10 transition-all duration-500 hover:-translate-y-1">
                {/* Gradient Border */}
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 rounded-lg p-[1px]">
                  <div className="h-full w-full bg-white dark:bg-gray-900 rounded-lg" />
                </div>

                {/* Content */}
                <div className="relative z-0">
                  {/* Thumbnail */}
                  {quiz.thumbnail && (
                    <div className="aspect-video w-full overflow-hidden rounded-t-lg relative">
                      <img
                        src={quiz.thumbnail}
                        alt={quiz.title}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent" />

                      {/* Favorite Button Overlay */}
                      <div className="absolute top-3 right-3">
                        <QuizFavoriteButton
                          quizId={quiz.id}
                          variant="ghost"
                          size="sm"
                          showText={false}
                          className="bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm hover:bg-white dark:hover:bg-gray-800 shadow-lg border-0"
                        />
                      </div>

                      {/* Type Badge */}
                      <div className="absolute top-3 left-3">
                        <Badge className="bg-white/90 dark:bg-gray-900/90 text-gray-900 dark:text-white backdrop-blur-sm border-0 shadow-lg">
                          <div className="flex items-center gap-1">
                            {getTypeIcon(quiz.type)}
                            <span className="text-xs font-medium">
                              {quiz.type.replace('_', ' ')}
                            </span>
                          </div>
                        </Badge>
                      </div>
                    </div>
                  )}

                  <CardContent className="p-6">
                    <div className="space-y-4">
                      {/* Header */}
                      <div className="space-y-2">
                        <div className="flex items-start justify-between gap-3">
                          <h3 className="font-bold text-xl group-hover:text-primary transition-colors line-clamp-2 leading-tight">
                            {quiz.title}
                          </h3>
                          {!quiz.thumbnail && (
                            <QuizFavoriteButton
                              quizId={quiz.id}
                              variant="ghost"
                              size="sm"
                              showText={false}
                              className="shrink-0"
                            />
                          )}
                        </div>
                        <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
                          {quiz.description}
                        </p>
                      </div>

                      {/* Metadata */}
                      <div className="flex items-center gap-2 flex-wrap">
                        <Badge
                          variant="outline"
                          className={`${getDifficultyColor(quiz.difficulty)} border-0 font-medium shadow-sm`}
                        >
                          {quiz.difficulty}
                        </Badge>
                        {quiz.tags.length > 0 && (
                          <Badge variant="outline" className="border-0 bg-gray-100 dark:bg-gray-800 font-medium shadow-sm">
                            {quiz.tags[0]}
                          </Badge>
                        )}
                        {quiz.isEnrolled && (
                          <Badge className="bg-gradient-to-r from-green-500 to-emerald-500 border-0 shadow-sm">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Enrolled
                          </Badge>
                        )}
                        {quiz.lastAttempt && (
                          <Badge variant="outline" className="border-0 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 text-blue-700 dark:text-blue-300 font-medium shadow-sm">
                            Last: {quiz.lastAttempt.percentage}%
                          </Badge>
                        )}
                      </div>

                      {/* Stats */}
                      <div className="grid grid-cols-2 gap-3">
                        <div className="flex items-center gap-2 p-2 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                          <div className="p-1 rounded bg-blue-100 dark:bg-blue-900/30">
                            <BookOpen className="h-3 w-3 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div className="text-xs">
                            <div className="font-medium">{quiz.questionCount}</div>
                            <div className="text-muted-foreground">questions</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 p-2 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                          <div className="p-1 rounded bg-purple-100 dark:bg-purple-900/30">
                            <Clock className="h-3 w-3 text-purple-600 dark:text-purple-400" />
                          </div>
                          <div className="text-xs">
                            <div className="font-medium">{quiz.timeLimit}</div>
                            <div className="text-muted-foreground">minutes</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 p-2 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                          <div className="p-1 rounded bg-orange-100 dark:bg-orange-900/30">
                            <Users className="h-3 w-3 text-orange-600 dark:text-orange-400" />
                          </div>
                          <div className="text-xs">
                            <div className="font-medium">{quiz.totalAttempts}</div>
                            <div className="text-muted-foreground">attempts</div>
                          </div>
                        </div>
                        <div className="flex items-center gap-2 p-2 rounded-lg bg-gray-50 dark:bg-gray-800/50">
                          <div className="p-1 rounded bg-green-100 dark:bg-green-900/30">
                            <Users className="h-3 w-3 text-green-600 dark:text-green-400" />
                          </div>
                          <div className="text-xs">
                            <div className="font-medium">{quiz.enrollmentCount}</div>
                            <div className="text-muted-foreground">enrolled</div>
                          </div>
                        </div>
                      </div>

                      {/* Instructor */}
                      <div className="flex items-center gap-3 p-3 rounded-lg bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800/50 dark:to-gray-700/50">
                        <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg">
                          {quiz.instructor.name.split(' ').map((n: string) => n[0]).join('')}
                        </div>
                        <div className="flex-1">
                          <div className="text-sm font-medium">Instructor</div>
                          <div className="text-xs text-muted-foreground">{quiz.instructor.name}</div>
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex items-center gap-2 pt-2">
                        {quiz.isEnrolled ? (
                          <Button
                            asChild
                            className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-300"
                          >
                            <Link href={`/student/quiz/${quiz.id}`}>
                              <Play className="h-4 w-4 mr-2" />
                              Start Quiz
                            </Link>
                          </Button>
                        ) : (
                          <Button
                            onClick={() => handleEnroll(quiz.id)}
                            className="flex-1 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 shadow-lg hover:shadow-xl transition-all duration-300"
                          >
                            Enroll Now
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          asChild
                          className="border-0 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 shadow-sm"
                        >
                          <Link href={`/student/browse/${quiz.id}`}>
                            <Eye className="h-4 w-4" />
                          </Link>
                        </Button>
                        <QuizShareButton
                          quizId={quiz.id}
                          variant="outline"
                          size="sm"
                          showText={false}
                          className="border-0 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 shadow-sm"
                        />
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

       
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-center gap-2 mt-8">
          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
            disabled={currentPage === 1}
          >
            Previous
          </Button>

          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              const page = currentPage <= 3 ? i + 1 : currentPage - 2 + i
              if (page > totalPages) return null

              return (
                <Button
                  key={page}
                  variant={currentPage === page ? "default" : "outline"}
                  size="sm"
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </Button>
              )
            })}
          </div>

          <Button
            variant="outline"
            onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
            disabled={currentPage === totalPages}
          >
            Next
          </Button>
        </div>
      )}
    </div>
  )
}