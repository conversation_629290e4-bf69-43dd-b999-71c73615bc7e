'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { 
  Clock, 
  Users, 
  Star, 
  BookOpen, 
  ShoppingCart, 
  CheckCircle,
  Play,
  Trophy,
  Target,
  Zap
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { RazorpayCheckout } from '@/components/payments/razorpay-checkout'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'
import { useSession } from 'next-auth/react'

interface Quiz {
  id: string
  title: string
  type: string
  difficulty: string
  timeLimit?: number
}

interface Bundle {
  id: string
  title: string
  description?: string
  shortDescription?: string
  price: number
  originalPrice?: number
  slug: string
  thumbnailImage?: string
  category?: string
  level?: string
  duration?: string
  tags: string[]
  features: string[]
  quizCount: number
  quizzes: Quiz[]
  purchaseCount: number
}

interface BundleCardProps {
  bundle: Bundle
  isPurchased?: boolean
  progress?: {
    totalQuizzes: number
    completedQuizzes: number
    progressPercentage: number
  }
  onPurchaseSuccess?: () => void
}

export function BundleCard({ bundle, isPurchased, progress, onPurchaseSuccess }: BundleCardProps) {
  const router = useRouter()
  const { data: session } = useSession()
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [purchasing, setPurchasing] = useState(false)

  const handlePurchase = async () => {
    if (!session) {
      toast.error('Please sign in to purchase bundles')
      router.push('/auth/signin')
      return
    }

    if (bundle.price === 0) {
      // Handle free bundle
      try {
        setPurchasing(true)
        const response = await fetch(`/api/quiz-bundles/${bundle.id}/purchase`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ paymentMethod: 'free' })
        })

        if (response.ok) {
          const data = await response.json()
          toast.success('Bundle purchased successfully!')
          if (onPurchaseSuccess) onPurchaseSuccess()
          router.push(data.data.redirectUrl || `/student/quiz-bundles/${bundle.slug}`)
        } else {
          const error = await response.json()
          toast.error(error.message || 'Failed to purchase bundle')
        }
      } catch (error) {
        console.error('Purchase error:', error)
        toast.error('Failed to purchase bundle')
      } finally {
        setPurchasing(false)
      }
    } else {
      // Handle paid bundle
      setShowPaymentModal(true)
    }
  }

  const handlePaymentSuccess = () => {
    toast.success('Payment successful! Welcome to the bundle!')
    if (onPurchaseSuccess) onPurchaseSuccess()
    setShowPaymentModal(false)
  }

  const handleViewBundle = () => {
    if (isPurchased) {
      router.push(`/student/quiz-bundles/${bundle.slug}`)
    } else {
      router.push(`/quiz-bundles/${bundle.slug}`)
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'hard': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getLevelIcon = (level?: string) => {
    switch (level?.toLowerCase()) {
      case 'beginner': return <Target className="h-4 w-4" />
      case 'intermediate': return <Zap className="h-4 w-4" />
      case 'advanced': return <Trophy className="h-4 w-4" />
      default: return <BookOpen className="h-4 w-4" />
    }
  }

  return (
    <>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
        whileHover={{ y: -5 }}
        className="h-full"
      >
        <Card className="h-full flex flex-col bg-white/95 backdrop-blur-xl border-0 shadow-lg hover:shadow-xl transition-all duration-300">
          <CardHeader className="p-3 pb-2">
            {/* Bundle Image */}
            <div className="relative h-32 sm:h-36 rounded-lg overflow-hidden mb-2">
              {bundle.thumbnailImage ? (
                <img
                  src={bundle.thumbnailImage}
                  alt={bundle.title}
                  className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                  onError={(e) => {
                    // Fallback to gradient if image fails to load
                    const target = e.target as HTMLImageElement
                    target.style.display = 'none'
                    const fallback = target.nextElementSibling as HTMLElement
                    if (fallback) fallback.style.display = 'flex'
                  }}
                />
              ) : null}
              <div
                className={`w-full h-full bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 flex items-center justify-center ${
                  bundle.thumbnailImage ? 'hidden' : 'flex'
                }`}
              >
                <div className="text-center">
                  <BookOpen className="h-12 w-12 text-white mx-auto mb-2" />
                  <p className="text-white text-xs font-medium opacity-80">Quiz Bundle</p>
                </div>
              </div>
              
              {/* Price Badge */}
              <div className="absolute top-3 right-3">
                {bundle.price === 0 ? (
                  <Badge className="bg-green-500 text-white">Free</Badge>
                ) : (
                  <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
                    <div className="flex items-center gap-1">
                      <span className="text-lg font-bold text-gray-900">₹{bundle.price}</span>
                      {bundle.originalPrice && bundle.originalPrice > bundle.price && (
                        <span className="text-sm text-gray-500 line-through">₹{bundle.originalPrice}</span>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Purchase Status */}
              {isPurchased && (
                <div className="absolute top-3 left-3">
                  <Badge className="bg-green-500 text-white flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    Purchased
                  </Badge>
                </div>
              )}
            </div>

            {/* Bundle Info */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  {bundle.level && (
                    <Badge variant="outline" className="flex items-center gap-1 text-xs px-2 py-0.5">
                      {getLevelIcon(bundle.level)}
                      {bundle.level}
                    </Badge>
                  )}
                  {bundle.category && (
                    <Badge variant="secondary" className="text-xs px-2 py-0.5">{bundle.category}</Badge>
                  )}
                </div>
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Users className="h-3 w-3" />
                  {bundle.purchaseCount}
                </div>
              </div>

              <h3 className="text-base font-bold text-gray-900 line-clamp-2 leading-tight">
                {bundle.title}
              </h3>

              <p className="text-gray-600 text-xs line-clamp-2 leading-tight">
                {bundle.shortDescription || bundle.description || "Enhance your skills with this comprehensive quiz bundle."}
              </p>

              {/* Tags - Now visible */}
              {bundle.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-1">
                  {bundle.tags.slice(0, 3).map(tag => (
                    <Badge key={tag} variant="secondary" className="text-xs px-1.5 py-0.5 bg-blue-50 text-blue-700 border-blue-200">
                      #{tag}
                    </Badge>
                  ))}
                  {bundle.tags.length > 3 && (
                    <Badge variant="secondary" className="text-xs px-1.5 py-0.5 bg-gray-50 text-gray-600">
                      +{bundle.tags.length - 3}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </CardHeader>

          <CardContent className="flex-1 px-3 py-2">
            {/* Progress (if purchased) */}
            {isPurchased && progress && (
              <div className="space-y-1 pb-2 border-b border-gray-100 mb-2">
                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-600">Progress</span>
                  <span className="font-medium">{progress.progressPercentage}%</span>
                </div>
                <Progress value={progress.progressPercentage} className="h-1.5" />
                <div className="text-xs text-gray-500">
                  {progress.completedQuizzes} of {progress.totalQuizzes} completed
                </div>
              </div>
            )}

            {/* Bundle Stats */}
            <div className="grid grid-cols-2 gap-2 text-xs mb-2">
              <div className="flex items-center gap-1.5 text-gray-600">
                <BookOpen className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">{bundle.quizCount} Quizzes</span>
              </div>
              <div className="flex items-center gap-1.5 text-gray-600">
                <Clock className="h-3 w-3 flex-shrink-0" />
                <span className="truncate">{bundle.duration || "30 min"}</span>
              </div>
            </div>

            {/* Quiz Types - Compact display */}
            {bundle.quizzes.length > 0 && (
              <div className="mb-2">
                <div className="flex flex-wrap gap-1">
                  {Array.from(new Set(bundle.quizzes.map(q => q.type))).slice(0, 2).map(type => (
                    <Badge key={type} variant="outline" className="text-xs px-1.5 py-0.5">
                      {type}
                    </Badge>
                  ))}
                  {Array.from(new Set(bundle.quizzes.map(q => q.type))).length > 2 && (
                    <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                      +{Array.from(new Set(bundle.quizzes.map(q => q.type))).length - 2}
                    </Badge>
                  )}
                </div>
              </div>
            )}

            {/* Features - Compact display */}
            {bundle.features.length > 0 && (
              <div className="space-y-0.5">
                {bundle.features.slice(0, 2).map((feature, index) => (
                  <div key={index} className="flex items-start gap-1.5">
                    <CheckCircle className="h-2.5 w-2.5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-xs text-gray-600 line-clamp-1">{feature}</span>
                  </div>
                ))}
                {bundle.features.length > 2 && (
                  <div className="text-xs text-gray-500">+{bundle.features.length - 2} more</div>
                )}
              </div>
            )}
          </CardContent>

          <CardFooter className="pt-2 px-3 pb-3">
            <div className="w-full space-y-1.5">
              {isPurchased ? (
                <Button
                  onClick={handleViewBundle}
                  className="w-full h-8 text-sm"
                  size="sm"
                >
                  <Play className="mr-1.5 h-3 w-3" />
                  Continue Learning
                </Button>
              ) : (
                <>
                  <Button
                    onClick={handlePurchase}
                    disabled={purchasing}
                    className="w-full h-8 text-sm"
                    size="sm"
                  >
                    {purchasing ? (
                      'Processing...'
                    ) : bundle.price === 0 ? (
                      <>
                        <CheckCircle className="mr-1.5 h-3 w-3" />
                        Get Free
                      </>
                    ) : (
                      <>
                        <ShoppingCart className="mr-1.5 h-3 w-3" />
                        ₹{bundle.price}
                      </>
                    )}
                  </Button>
                  <Button
                    variant="outline"
                    onClick={handleViewBundle}
                    className="w-full h-7 text-xs"
                    size="sm"
                  >
                    View Details
                  </Button>
                </>
              )}
            </div>
          </CardFooter>
        </Card>
      </motion.div>

      {/* Payment Modal */}
      {session && (
        <RazorpayCheckout
          bundle={{
            id: bundle.id,
            title: bundle.title,
            price: bundle.price,
            slug: bundle.slug
          }}
          user={{
            name: session.user.name || '',
            email: session.user.email || ''
          }}
          paymentType="bundle"
          isOpen={showPaymentModal}
          onClose={() => setShowPaymentModal(false)}
          onSuccess={handlePaymentSuccess}
        />
      )}
    </>
  )
}
