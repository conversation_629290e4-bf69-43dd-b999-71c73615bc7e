import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { notificationService } from '@/lib/notification-service'

// POST /api/test/notifications - Test notification system (Admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { type = 'info', title = 'Test Notification', message = 'This is a test notification', targetUserId } = body

    // Send test notification
    const notificationData = {
      type: 'SYSTEM_UPDATE' as const,
      title,
      message,
      priority: 'normal' as const,
      category: 'test'
    }

    let notificationId: string

    if (targetUserId) {
      // Send to specific user
      notificationId = await notificationService.sendToUsers(
        [{ userId: targetUserId }],
        notificationData
      )
    } else {
      // Send to current user
      notificationId = await notificationService.sendToUsers(
        [{ userId: session.user.id }],
        notificationData
      )
    }

    return NextResponse.json({
      success: true,
      notificationId,
      message: 'Test notification sent successfully'
    })
  } catch (error) {
    console.error('Error sending test notification:', error)
    return NextResponse.json(
      { error: 'Failed to send test notification' },
      { status: 500 }
    )
  }
}
